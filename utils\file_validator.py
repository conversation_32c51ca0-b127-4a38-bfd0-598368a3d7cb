from fastapi import UploadFile, HTTPException
from typing import Set
import os

# Supported file types
SUPPORTED_EXTENSIONS: Set[str] = {
    '.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.tif', '.bmp', '.webp'
}

SUPPORTED_MIME_TYPES: Set[str] = {
    'application/pdf',
    'image/png',
    'image/jpeg',
    'image/tiff',
    'image/bmp',
    'image/webp'
}

# Maximum file size (50MB)
MAX_FILE_SIZE = 50 * 1024 * 1024

def validate_file(file: UploadFile) -> None:
    """
    Validate uploaded file for type and size
    
    Args:
        file: The uploaded file to validate
        
    Raises:
        HTTPException: If file is invalid
    """
    # Check if file is provided
    if not file or not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Check file extension
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in SUPPORTED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type. Supported types: {', '.join(SUPPORTED_EXTENSIONS)}"
        )
    
    # Check content type
    if file.content_type and file.content_type not in SUPPORTED_MIME_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported content type: {file.content_type}"
        )
    
    # Check file size
    if hasattr(file, 'size') and file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
        )

def get_file_type(file_path: str) -> str:
    """
    Determine file type from file path
    
    Args:
        file_path: Path to the file
        
    Returns:
        File type ('pdf' or 'image')
    """
    extension = os.path.splitext(file_path)[1].lower()
    if extension == '.pdf':
        return 'pdf'
    else:
        return 'image'
