from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
import io
import logging
from pathlib import Path
import tempfile
import os

from services.doctr_service import DocTRService
from models.response_models import TextractResponse, KeyValuePair, BoundingBox
from utils.file_validator import validate_file
from utils.error_handlers import (
    DocumentProcessingError, FileValidationError, ModelInitializationError,
    document_processing_exception_handler, file_validation_exception_handler,
    model_initialization_exception_handler, general_exception_handler
)
from config import Config

# Configure logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=Config.API_TITLE,
    description=Config.API_DESCRIPTION,
    version=Config.API_VERSION
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add exception handlers
app.add_exception_handler(DocumentProcessingError, document_processing_exception_handler)
app.add_exception_handler(FileValidationError, file_validation_exception_handler)
app.add_exception_handler(ModelInitializationError, model_initialization_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# Initialize DocTR service
doctr_service = DocTRService()

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup"""
    logger.info("Starting DocTR Text Extraction API")
    try:
        await doctr_service.initialize()
        logger.info("DocTR service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize DocTR service: {e}")
        raise ModelInitializationError(f"Failed to initialize DocTR service: {e}")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "DocTR Text Extraction API is running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "doctr-text-extraction"}

@app.post("/extract-text", response_model=TextractResponse)
async def extract_text(
    file: UploadFile = File(...),
    extract_key_values: bool = True,
    confidence_threshold: float = 0.5
):
    """
    Extract text and key-value pairs from uploaded document
    
    Args:
        file: PDF or image file to process
        extract_key_values: Whether to extract key-value pairs
        confidence_threshold: Minimum confidence score for text detection
    
    Returns:
        TextractResponse with extracted text, key-value pairs, and metadata
    """
    try:
        # Validate file
        try:
            validate_file(file)
        except HTTPException as e:
            raise FileValidationError(e.detail)

        # Read file content
        file_content = await file.read()

        if not file_content:
            raise FileValidationError("Empty file provided")

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Process document with DocTR
            result = await doctr_service.process_document(
                temp_file_path,
                extract_key_values=extract_key_values,
                confidence_threshold=confidence_threshold
            )

            logger.info(f"Successfully processed document: {file.filename}")
            return result

        except Exception as e:
            logger.error(f"Error processing document {file.filename}: {str(e)}")
            raise DocumentProcessingError(f"Failed to process document: {str(e)}")

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except (FileValidationError, DocumentProcessingError):
        # Re-raise custom exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing document: {str(e)}")
        raise DocumentProcessingError(f"Unexpected error: {str(e)}")

@app.post("/extract-text-batch", response_model=List[TextractResponse])
async def extract_text_batch(
    files: List[UploadFile] = File(...),
    extract_key_values: bool = True,
    confidence_threshold: float = 0.5
):
    """
    Extract text and key-value pairs from multiple documents
    
    Args:
        files: List of PDF or image files to process
        extract_key_values: Whether to extract key-value pairs
        confidence_threshold: Minimum confidence score for text detection
    
    Returns:
        List of TextractResponse objects
    """
    results = []
    
    for file in files:
        try:
            # Process each file individually
            result = await extract_text(file, extract_key_values, confidence_threshold)
            results.append(result)
        except Exception as e:
            logger.error(f"Error processing file {file.filename}: {str(e)}")
            # Add error result for this file
            error_result = TextractResponse(
                document_metadata={
                    "filename": file.filename,
                    "error": str(e)
                },
                blocks=[],
                key_value_pairs=[],
                confidence_scores={}
            )
            results.append(error_result)
    
    return results

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
