from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from enum import Enum

class BlockType(str, Enum):
    """Types of text blocks similar to Amazon Textract"""
    PAGE = "PAGE"
    LINE = "LINE"
    WORD = "WORD"
    KEY_VALUE_SET = "KEY_VALUE_SET"
    KEY = "KEY"
    VALUE = "VALUE"
    TABLE = "TABLE"
    CELL = "CELL"

class BoundingBox(BaseModel):
    """Bounding box coordinates (normalized 0-1)"""
    width: float = Field(..., description="Width of bounding box (0-1)")
    height: float = Field(..., description="Height of bounding box (0-1)")
    left: float = Field(..., description="Left position (0-1)")
    top: float = Field(..., description="Top position (0-1)")

class Geometry(BaseModel):
    """Geometry information for text blocks"""
    bounding_box: BoundingBox
    polygon: Optional[List[Dict[str, float]]] = Field(None, description="Polygon points")

class Relationship(BaseModel):
    """Relationships between text blocks"""
    type: str = Field(..., description="Type of relationship (CHILD, VALUE, etc.)")
    ids: List[str] = Field(..., description="IDs of related blocks")

class TextBlock(BaseModel):
    """Text block similar to Amazon Textract Block"""
    id: str = Field(..., description="Unique identifier for the block")
    block_type: BlockType = Field(..., description="Type of the block")
    confidence: float = Field(..., description="Confidence score (0-100)")
    text: Optional[str] = Field(None, description="Detected text")
    geometry: Geometry = Field(..., description="Bounding box and polygon")
    relationships: Optional[List[Relationship]] = Field(None, description="Relationships to other blocks")
    page: int = Field(1, description="Page number")

class KeyValuePair(BaseModel):
    """Key-value pair extracted from document"""
    key: str = Field(..., description="The key text")
    value: str = Field(..., description="The value text")
    key_confidence: float = Field(..., description="Confidence score for key (0-100)")
    value_confidence: float = Field(..., description="Confidence score for value (0-100)")
    key_geometry: Geometry = Field(..., description="Geometry of key text")
    value_geometry: Geometry = Field(..., description="Geometry of value text")
    key_block_id: str = Field(..., description="ID of key text block")
    value_block_id: str = Field(..., description="ID of value text block")

class DocumentMetadata(BaseModel):
    """Document metadata"""
    filename: Optional[str] = None
    pages: int = Field(1, description="Number of pages")
    width: Optional[int] = Field(None, description="Document width in pixels")
    height: Optional[int] = Field(None, description="Document height in pixels")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    error: Optional[str] = Field(None, description="Error message if processing failed")

class TextractResponse(BaseModel):
    """Main response model similar to Amazon Textract"""
    document_metadata: DocumentMetadata = Field(..., description="Document metadata")
    blocks: List[TextBlock] = Field(..., description="All detected text blocks")
    key_value_pairs: List[KeyValuePair] = Field(..., description="Extracted key-value pairs")
    confidence_scores: Dict[str, float] = Field(..., description="Overall confidence scores")
    
    class Config:
        json_encoders = {
            # Custom encoders if needed
        }
