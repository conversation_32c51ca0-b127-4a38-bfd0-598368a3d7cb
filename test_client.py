#!/usr/bin/env python3
"""
Test client for DocTR Text Extraction API
"""

import requests
import json
import time
from pathlib import Path
import argparse

class DocTRTestClient:
    """Test client for the DocTR API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        
    def health_check(self):
        """Check if the API is running"""
        try:
            response = requests.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ API is running")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to API. Make sure the server is running.")
            return False
    
    def extract_text(self, file_path: str, extract_key_values: bool = True, confidence_threshold: float = 0.5):
        """Extract text from a single document"""
        if not Path(file_path).exists():
            print(f"❌ File not found: {file_path}")
            return None
            
        print(f"📄 Processing file: {file_path}")
        start_time = time.time()
        
        try:
            with open(file_path, 'rb') as file:
                files = {"file": file}
                data = {
                    "extract_key_values": extract_key_values,
                    "confidence_threshold": confidence_threshold
                }
                
                response = requests.post(
                    f"{self.base_url}/extract-text",
                    files=files,
                    data=data
                )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Processing completed in {processing_time:.2f}s")
                self._print_results(result)
                return result
            else:
                print(f"❌ Error: {response.status_code}")
                print(response.json())
                return None
                
        except Exception as e:
            print(f"❌ Error processing file: {e}")
            return None
    
    def extract_text_batch(self, file_paths: list, extract_key_values: bool = True, confidence_threshold: float = 0.5):
        """Extract text from multiple documents"""
        existing_files = [f for f in file_paths if Path(f).exists()]
        if not existing_files:
            print("❌ No valid files found")
            return None
            
        print(f"📄 Processing {len(existing_files)} files...")
        start_time = time.time()
        
        try:
            files = [("files", open(file_path, 'rb')) for file_path in existing_files]
            data = {
                "extract_key_values": extract_key_values,
                "confidence_threshold": confidence_threshold
            }
            
            response = requests.post(
                f"{self.base_url}/extract-text-batch",
                files=files,
                data=data
            )
            
            # Close file handles
            for _, file_handle in files:
                file_handle.close()
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ Batch processing completed in {processing_time:.2f}s")
                
                for i, result in enumerate(results):
                    print(f"\n--- Result {i+1}: {existing_files[i]} ---")
                    self._print_results(result)
                
                return results
            else:
                print(f"❌ Error: {response.status_code}")
                print(response.json())
                return None
                
        except Exception as e:
            print(f"❌ Error processing files: {e}")
            return None
    
    def _print_results(self, result: dict):
        """Print extraction results in a readable format"""
        metadata = result.get("document_metadata", {})
        blocks = result.get("blocks", [])
        key_value_pairs = result.get("key_value_pairs", [])
        confidence_scores = result.get("confidence_scores", {})
        
        print(f"📊 Document Info:")
        print(f"   - Pages: {metadata.get('pages', 'N/A')}")
        print(f"   - Dimensions: {metadata.get('width', 'N/A')}x{metadata.get('height', 'N/A')}")
        print(f"   - Processing time: {metadata.get('processing_time', 'N/A'):.2f}s")
        
        print(f"\n📈 Confidence Scores:")
        print(f"   - Overall: {confidence_scores.get('overall', 0):.1f}%")
        print(f"   - Words: {confidence_scores.get('words', 0):.1f}%")
        print(f"   - Lines: {confidence_scores.get('lines', 0):.1f}%")
        
        word_blocks = [b for b in blocks if b.get('block_type') == 'WORD']
        print(f"\n📝 Text Blocks: {len(word_blocks)} words detected")
        
        if key_value_pairs:
            print(f"\n🔑 Key-Value Pairs ({len(key_value_pairs)} found):")
            for i, kv in enumerate(key_value_pairs[:10]):  # Show first 10
                print(f"   {i+1}. {kv['key']}: {kv['value']}")
                print(f"      Confidence: Key={kv['key_confidence']:.1f}%, Value={kv['value_confidence']:.1f}%")
            
            if len(key_value_pairs) > 10:
                print(f"   ... and {len(key_value_pairs) - 10} more")
        else:
            print("\n🔑 No key-value pairs found")

def main():
    parser = argparse.ArgumentParser(description="Test DocTR Text Extraction API")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--file", help="Single file to process")
    parser.add_argument("--files", nargs="+", help="Multiple files to process")
    parser.add_argument("--no-kv", action="store_true", help="Skip key-value extraction")
    parser.add_argument("--confidence", type=float, default=0.5, help="Confidence threshold")
    
    args = parser.parse_args()
    
    client = DocTRTestClient(args.url)
    
    # Health check
    if not client.health_check():
        return
    
    extract_kv = not args.no_kv
    
    if args.file:
        # Single file processing
        client.extract_text(args.file, extract_kv, args.confidence)
    elif args.files:
        # Batch processing
        client.extract_text_batch(args.files, extract_kv, args.confidence)
    else:
        print("Please provide --file or --files argument")
        print("Example: python test_client.py --file document.pdf")
        print("Example: python test_client.py --files doc1.pdf doc2.jpg")

if __name__ == "__main__":
    main()
