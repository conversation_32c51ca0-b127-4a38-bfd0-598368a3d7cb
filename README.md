# DocTR Text Extraction API

A FastAPI application that uses doc<PERSON> (Document Text Recognition) by <PERSON><PERSON> for text block extraction, layout detection, and reading. The application extracts key-value pairs from documents similar to Amazon Textract.

## Features

- **Text Extraction**: Extract text from PDF and image files using docTR
- **Layout Detection**: Detect text blocks, lines, and words with bounding boxes
- **Key-Value Pair Extraction**: Intelligent extraction of key-value pairs similar to Amazon Textract
- **Multiple File Formats**: Support for PDF, PNG, JPG, JPEG, TIFF, BMP, and WebP
- **Batch Processing**: Process multiple documents in a single request
- **Confidence Scores**: Provides confidence scores for all detected text
- **RESTful API**: Easy-to-use REST endpoints with comprehensive error handling

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd doctr-text-extraction
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. For PDF support, install poppler:
   - **Windows**: Download from https://poppler.freedesktop.org/
   - **macOS**: `brew install poppler`
   - **Ubuntu/Debian**: `sudo apt-get install poppler-utils`

## Usage

### Start the Server

```bash
python app.py
```

Or using uvicorn directly:
```bash
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at `http://localhost:8000`

### API Documentation

Interactive API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### Endpoints

#### 1. Extract Text from Single Document

**POST** `/extract-text`

Extract text and key-value pairs from a single document.

**Parameters:**
- `file`: The document file (PDF or image)
- `extract_key_values`: Whether to extract key-value pairs (default: true)
- `confidence_threshold`: Minimum confidence score for text detection (default: 0.5)

**Example using curl:**
```bash
curl -X POST "http://localhost:8000/extract-text" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "extract_key_values=true" \
  -F "confidence_threshold=0.5"
```

**Example using Python:**
```python
import requests

url = "http://localhost:8000/extract-text"
files = {"file": open("document.pdf", "rb")}
data = {
    "extract_key_values": True,
    "confidence_threshold": 0.5
}

response = requests.post(url, files=files, data=data)
result = response.json()
```

#### 2. Extract Text from Multiple Documents

**POST** `/extract-text-batch`

Process multiple documents in a single request.

**Example using Python:**
```python
import requests

url = "http://localhost:8000/extract-text-batch"
files = [
    ("files", open("document1.pdf", "rb")),
    ("files", open("document2.jpg", "rb"))
]
data = {
    "extract_key_values": True,
    "confidence_threshold": 0.5
}

response = requests.post(url, files=files, data=data)
results = response.json()
```

#### 3. Health Check

**GET** `/health`

Check if the service is running.

### Response Format

The API returns responses in a format similar to Amazon Textract:

```json
{
  "document_metadata": {
    "filename": "document.pdf",
    "pages": 1,
    "width": 1200,
    "height": 1600,
    "processing_time": 2.34
  },
  "blocks": [
    {
      "id": "block-uuid",
      "block_type": "WORD",
      "confidence": 95.5,
      "text": "Invoice",
      "geometry": {
        "bounding_box": {
          "left": 0.1,
          "top": 0.05,
          "width": 0.15,
          "height": 0.03
        }
      },
      "page": 1
    }
  ],
  "key_value_pairs": [
    {
      "key": "Invoice Number",
      "value": "INV-2023-001",
      "key_confidence": 98.2,
      "value_confidence": 96.7,
      "key_geometry": {...},
      "value_geometry": {...},
      "key_block_id": "key-block-uuid",
      "value_block_id": "value-block-uuid"
    }
  ],
  "confidence_scores": {
    "overall": 94.5,
    "words": 95.2,
    "lines": 93.8
  }
}
```

## Configuration

You can configure the application using environment variables:

- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `DEBUG`: Enable debug mode (default: false)
- `LOG_LEVEL`: Logging level (default: INFO)
- `DOCTR_DET_ARCH`: Detection architecture (default: db_resnet50)
- `DOCTR_RECO_ARCH`: Recognition architecture (default: crnn_vgg16_bn)

## Error Handling

The API provides comprehensive error handling with standardized error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Unsupported file type",
    "type": "FileValidationError"
  }
}
```

## Supported File Types

- **PDF**: .pdf
- **Images**: .png, .jpg, .jpeg, .tiff, .tif, .bmp, .webp

## Limitations

- Maximum file size: 50MB
- PDF files are converted to images at 200 DPI
- Processing time depends on document size and complexity

## License

This project is licensed under the MIT License.
