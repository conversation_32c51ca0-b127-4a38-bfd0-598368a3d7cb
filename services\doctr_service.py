import asyncio
import time
import uuid
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, Optional
import logging
import numpy as np
from PIL import Image
import cv2

from doctr.io import DocumentFile
from doctr.models import ocr_predictor
from pdf2image import convert_from_path

from models.response_models import (
    TextractResponse, TextBlock, KeyValuePair, BoundingBox, 
    Geometry, DocumentMetadata, BlockType, Relationship
)
from utils.file_validator import get_file_type
from services.key_value_extractor import KeyValueExtractor

logger = logging.getLogger(__name__)

class DocTRService:
    """Service for handling docTR operations and text extraction"""
    
    def __init__(self):
        self.model = None
        self.key_value_extractor = KeyValueExtractor()
        
    async def initialize(self):
        """Initialize the docTR model"""
        try:
            logger.info("Initializing docTR model...")
            # Use a lighter model for faster processing
            self.model = ocr_predictor(
                det_arch='db_resnet50',
                reco_arch='crnn_vgg16_bn',
                pretrained=True
            )
            logger.info("DocTR model initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize docTR model: {e}")
            raise
    
    async def process_document(
        self, 
        file_path: str, 
        extract_key_values: bool = True,
        confidence_threshold: float = 0.5
    ) -> TextractResponse:
        """
        Process document and extract text with key-value pairs
        
        Args:
            file_path: Path to the document file
            extract_key_values: Whether to extract key-value pairs
            confidence_threshold: Minimum confidence for text detection
            
        Returns:
            TextractResponse with extracted data
        """
        start_time = time.time()
        
        try:
            # Load document
            file_type = get_file_type(file_path)
            
            if file_type == 'pdf':
                # Convert PDF to images
                images = convert_from_path(file_path, dpi=200)
                doc_images = [np.array(img) for img in images]
            else:
                # Load single image
                image = Image.open(file_path)
                doc_images = [np.array(image)]
            
            # Process with docTR
            doc = DocumentFile.from_images(doc_images)
            result = self.model(doc)
            
            # Extract text blocks
            blocks = []
            all_text_items = []
            
            for page_idx, page in enumerate(result.pages):
                page_blocks, page_text_items = self._extract_page_blocks(
                    page, page_idx + 1, confidence_threshold
                )
                blocks.extend(page_blocks)
                all_text_items.extend(page_text_items)
            
            # Extract key-value pairs if requested
            key_value_pairs = []
            if extract_key_values and all_text_items:
                key_value_pairs = await self.key_value_extractor.extract_key_value_pairs(
                    all_text_items, blocks
                )
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(blocks)
            
            # Create document metadata
            processing_time = time.time() - start_time
            document_metadata = DocumentMetadata(
                filename=file_path.split('/')[-1],
                pages=len(result.pages),
                width=doc_images[0].shape[1] if doc_images else None,
                height=doc_images[0].shape[0] if doc_images else None,
                processing_time=processing_time
            )
            
            return TextractResponse(
                document_metadata=document_metadata,
                blocks=blocks,
                key_value_pairs=key_value_pairs,
                confidence_scores=confidence_scores
            )
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            raise
    
    def _extract_page_blocks(
        self, 
        page, 
        page_num: int, 
        confidence_threshold: float
    ) -> Tuple[List[TextBlock], List[Dict]]:
        """Extract text blocks from a single page"""
        blocks = []
        text_items = []
        
        # Create page block
        page_id = str(uuid.uuid4())
        page_block = TextBlock(
            id=page_id,
            block_type=BlockType.PAGE,
            confidence=100.0,
            geometry=Geometry(
                bounding_box=BoundingBox(width=1.0, height=1.0, left=0.0, top=0.0)
            ),
            page=page_num
        )
        blocks.append(page_block)
        
        line_ids = []
        
        # Process each text block in the page
        for block_idx, block in enumerate(page.blocks):
            for line_idx, line in enumerate(block.lines):
                line_id = str(uuid.uuid4())
                line_ids.append(line_id)
                
                # Extract line text and confidence
                line_text = ""
                word_confidences = []
                word_ids = []
                
                for word in line.words:
                    word_confidence = float(word.confidence) * 100
                    if word_confidence >= confidence_threshold * 100:
                        word_text = word.value
                        line_text += word_text + " "
                        word_confidences.append(word_confidence)
                        
                        # Create word block
                        word_id = str(uuid.uuid4())
                        word_ids.append(word_id)
                        
                        word_geometry = self._create_geometry_from_coords(word.geometry)
                        word_block = TextBlock(
                            id=word_id,
                            block_type=BlockType.WORD,
                            confidence=word_confidence,
                            text=word_text,
                            geometry=word_geometry,
                            page=page_num
                        )
                        blocks.append(word_block)
                        
                        # Store text item for key-value extraction
                        text_items.append({
                            'text': word_text,
                            'confidence': word_confidence,
                            'geometry': word_geometry,
                            'block_id': word_id,
                            'page': page_num
                        })
                
                # Create line block if it has text
                if line_text.strip():
                    line_confidence = np.mean(word_confidences) if word_confidences else 0.0
                    line_geometry = self._create_geometry_from_coords(line.geometry)
                    
                    line_block = TextBlock(
                        id=line_id,
                        block_type=BlockType.LINE,
                        confidence=line_confidence,
                        text=line_text.strip(),
                        geometry=line_geometry,
                        relationships=[
                            Relationship(type="CHILD", ids=word_ids)
                        ] if word_ids else None,
                        page=page_num
                    )
                    blocks.append(line_block)
        
        # Add relationships to page block
        if line_ids:
            page_block.relationships = [Relationship(type="CHILD", ids=line_ids)]
        
        return blocks, text_items
    
    def _create_geometry_from_coords(self, geometry) -> Geometry:
        """Convert docTR geometry to our Geometry format"""
        # docTR returns normalized coordinates
        coords = geometry
        
        # Calculate bounding box
        min_x = min(point[0] for point in coords)
        max_x = max(point[0] for point in coords)
        min_y = min(point[1] for point in coords)
        max_y = max(point[1] for point in coords)
        
        bounding_box = BoundingBox(
            left=min_x,
            top=min_y,
            width=max_x - min_x,
            height=max_y - min_y
        )
        
        # Convert polygon points
        polygon = [{"x": point[0], "y": point[1]} for point in coords]
        
        return Geometry(
            bounding_box=bounding_box,
            polygon=polygon
        )
    
    def _calculate_confidence_scores(self, blocks: List[TextBlock]) -> Dict[str, float]:
        """Calculate overall confidence scores"""
        word_blocks = [b for b in blocks if b.block_type == BlockType.WORD]
        line_blocks = [b for b in blocks if b.block_type == BlockType.LINE]
        
        if not word_blocks:
            return {"overall": 0.0, "words": 0.0, "lines": 0.0}
        
        word_confidence = np.mean([b.confidence for b in word_blocks])
        line_confidence = np.mean([b.confidence for b in line_blocks]) if line_blocks else 0.0
        overall_confidence = (word_confidence + line_confidence) / 2 if line_blocks else word_confidence
        
        return {
            "overall": float(overall_confidence),
            "words": float(word_confidence),
            "lines": float(line_confidence)
        }
