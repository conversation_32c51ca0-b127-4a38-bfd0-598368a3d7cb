import re
import uuid
from typing import List, Dict, Any, Tuple, Optional
import logging
from dataclasses import dataclass

from models.response_models import KeyValuePair, TextBlock, Geometry, BoundingBox, BlockType, Relationship

logger = logging.getLogger(__name__)

@dataclass
class TextItem:
    """Represents a text item with position and metadata"""
    text: str
    confidence: float
    geometry: Geometry
    block_id: str
    page: int
    x: float  # Center x coordinate
    y: float  # Center y coordinate

class KeyValueExtractor:
    """Extract key-value pairs from detected text items"""
    
    def __init__(self):
        # Common patterns for key-value pairs
        self.key_patterns = [
            r'^[A-Za-z\s]+:$',  # Text ending with colon
            r'^[A-Za-z\s]+\s*:$',  # Text with spaces ending with colon
            r'^\w+\s*\w*\s*:$',  # Word(s) ending with colon
        ]
        
        # Common key indicators
        self.key_indicators = [
            'name', 'address', 'phone', 'email', 'date', 'amount', 'total', 'subtotal',
            'tax', 'invoice', 'number', 'id', 'customer', 'vendor', 'company', 'title',
            'description', 'quantity', 'price', 'cost', 'payment', 'due', 'balance',
            'account', 'reference', 'order', 'item', 'product', 'service', 'bill',
            'receipt', 'transaction', 'method', 'type', 'status', 'code', 'zip',
            'city', 'state', 'country', 'street', 'po box', 'suite', 'apt', 'unit'
        ]
        
        # Proximity thresholds (normalized coordinates)
        self.horizontal_threshold = 0.3  # Maximum horizontal distance
        self.vertical_threshold = 0.05   # Maximum vertical distance
        
    async def extract_key_value_pairs(
        self, 
        text_items: List[Dict], 
        blocks: List[TextBlock]
    ) -> List[KeyValuePair]:
        """
        Extract key-value pairs from text items
        
        Args:
            text_items: List of detected text items with geometry
            blocks: List of all text blocks
            
        Returns:
            List of extracted key-value pairs
        """
        try:
            # Convert to TextItem objects for easier processing
            items = self._convert_to_text_items(text_items)
            
            # Sort items by position (top to bottom, left to right)
            items.sort(key=lambda x: (x.y, x.x))
            
            # Find potential key-value pairs
            key_value_pairs = []
            
            # Method 1: Colon-based detection
            colon_pairs = self._extract_colon_based_pairs(items)
            key_value_pairs.extend(colon_pairs)
            
            # Method 2: Proximity-based detection
            proximity_pairs = self._extract_proximity_based_pairs(items)
            key_value_pairs.extend(proximity_pairs)
            
            # Method 3: Pattern-based detection
            pattern_pairs = self._extract_pattern_based_pairs(items)
            key_value_pairs.extend(pattern_pairs)
            
            # Remove duplicates and filter by confidence
            filtered_pairs = self._filter_and_deduplicate(key_value_pairs)
            
            return filtered_pairs
            
        except Exception as e:
            logger.error(f"Error extracting key-value pairs: {e}")
            return []
    
    def _convert_to_text_items(self, text_items: List[Dict]) -> List[TextItem]:
        """Convert text items to TextItem objects"""
        items = []
        for item in text_items:
            geometry = item['geometry']
            bbox = geometry.bounding_box
            
            # Calculate center coordinates
            center_x = bbox.left + bbox.width / 2
            center_y = bbox.top + bbox.height / 2
            
            text_item = TextItem(
                text=item['text'].strip(),
                confidence=item['confidence'],
                geometry=geometry,
                block_id=item['block_id'],
                page=item['page'],
                x=center_x,
                y=center_y
            )
            items.append(text_item)
        
        return items
    
    def _extract_colon_based_pairs(self, items: List[TextItem]) -> List[KeyValuePair]:
        """Extract key-value pairs based on colon patterns"""
        pairs = []
        
        for i, item in enumerate(items):
            # Check if item ends with colon (potential key)
            if item.text.endswith(':'):
                key_text = item.text[:-1].strip()  # Remove colon
                
                if not key_text:
                    continue
                
                # Look for value in nearby items
                value_item = self._find_nearest_value(item, items[i+1:])
                
                if value_item:
                    pair = KeyValuePair(
                        key=key_text,
                        value=value_item.text,
                        key_confidence=item.confidence,
                        value_confidence=value_item.confidence,
                        key_geometry=item.geometry,
                        value_geometry=value_item.geometry,
                        key_block_id=item.block_id,
                        value_block_id=value_item.block_id
                    )
                    pairs.append(pair)
        
        return pairs
    
    def _extract_proximity_based_pairs(self, items: List[TextItem]) -> List[KeyValuePair]:
        """Extract key-value pairs based on spatial proximity"""
        pairs = []
        used_items = set()
        
        for i, item in enumerate(items):
            if item.block_id in used_items:
                continue
                
            # Check if item looks like a key
            if self._is_potential_key(item.text):
                # Find nearby potential values
                for j, other_item in enumerate(items[i+1:], i+1):
                    if other_item.block_id in used_items:
                        continue
                    
                    # Check proximity
                    if self._are_items_related(item, other_item):
                        # Check if other item looks like a value
                        if self._is_potential_value(other_item.text, item.text):
                            pair = KeyValuePair(
                                key=item.text,
                                value=other_item.text,
                                key_confidence=item.confidence,
                                value_confidence=other_item.confidence,
                                key_geometry=item.geometry,
                                value_geometry=other_item.geometry,
                                key_block_id=item.block_id,
                                value_block_id=other_item.block_id
                            )
                            pairs.append(pair)
                            used_items.add(item.block_id)
                            used_items.add(other_item.block_id)
                            break
        
        return pairs
    
    def _extract_pattern_based_pairs(self, items: List[TextItem]) -> List[KeyValuePair]:
        """Extract key-value pairs based on common patterns"""
        pairs = []
        
        # Pattern: "Key Value" in same text item
        for item in items:
            text = item.text
            
            # Look for patterns like "Name: John Doe" or "Total: $100.00"
            colon_match = re.search(r'^([^:]+):\s*(.+)$', text)
            if colon_match:
                key_text = colon_match.group(1).strip()
                value_text = colon_match.group(2).strip()
                
                if key_text and value_text:
                    # Create separate geometries for key and value
                    key_geometry, value_geometry = self._split_geometry(item.geometry, len(key_text), len(text))
                    
                    pair = KeyValuePair(
                        key=key_text,
                        value=value_text,
                        key_confidence=item.confidence,
                        value_confidence=item.confidence,
                        key_geometry=key_geometry,
                        value_geometry=value_geometry,
                        key_block_id=item.block_id,
                        value_block_id=item.block_id
                    )
                    pairs.append(pair)
        
        return pairs
    
    def _find_nearest_value(self, key_item: TextItem, candidate_items: List[TextItem]) -> Optional[TextItem]:
        """Find the nearest potential value for a key"""
        best_candidate = None
        min_distance = float('inf')
        
        for item in candidate_items:
            # Skip if too far away
            if not self._are_items_related(key_item, item):
                continue
            
            # Calculate distance
            distance = abs(item.x - key_item.x) + abs(item.y - key_item.y)
            
            if distance < min_distance and self._is_potential_value(item.text, key_item.text):
                min_distance = distance
                best_candidate = item
        
        return best_candidate
    
    def _are_items_related(self, item1: TextItem, item2: TextItem) -> bool:
        """Check if two items are spatially related"""
        # Same page
        if item1.page != item2.page:
            return False
        
        # Check horizontal and vertical proximity
        horizontal_distance = abs(item2.x - item1.x)
        vertical_distance = abs(item2.y - item1.y)
        
        return (horizontal_distance <= self.horizontal_threshold and 
                vertical_distance <= self.vertical_threshold)
    
    def _is_potential_key(self, text: str) -> bool:
        """Check if text looks like a potential key"""
        text_lower = text.lower().strip()
        
        # Check for key indicators
        for indicator in self.key_indicators:
            if indicator in text_lower:
                return True
        
        # Check for patterns
        for pattern in self.key_patterns:
            if re.match(pattern, text):
                return True
        
        # Check if it's a short descriptive text
        if len(text.split()) <= 3 and text.isalpha():
            return True
        
        return False
    
    def _is_potential_value(self, text: str, key_text: str) -> bool:
        """Check if text looks like a potential value for the given key"""
        # Skip very short or very long texts
        if len(text) < 1 or len(text) > 200:
            return False
        
        # Skip if it looks like another key
        if text.endswith(':') or self._is_potential_key(text):
            return False
        
        # Check for common value patterns
        value_patterns = [
            r'^\d+$',  # Numbers
            r'^\$\d+\.?\d*$',  # Currency
            r'^[\w\s\-\.@]+$',  # Alphanumeric with common chars
            r'^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}$',  # Dates
            r'^[\w\.\-]+@[\w\.\-]+$',  # Email
            r'^\(\d{3}\)\s?\d{3}\-\d{4}$',  # Phone
        ]
        
        for pattern in value_patterns:
            if re.match(pattern, text):
                return True
        
        return True  # Default to true for flexibility
    
    def _split_geometry(self, geometry: Geometry, key_length: int, total_length: int) -> Tuple[Geometry, Geometry]:
        """Split geometry for key and value parts"""
        bbox = geometry.bounding_box
        
        # Estimate split point based on text length ratio
        split_ratio = key_length / total_length
        split_x = bbox.left + bbox.width * split_ratio
        
        # Key geometry (left part)
        key_bbox = BoundingBox(
            left=bbox.left,
            top=bbox.top,
            width=bbox.width * split_ratio,
            height=bbox.height
        )
        key_geometry = Geometry(bounding_box=key_bbox)
        
        # Value geometry (right part)
        value_bbox = BoundingBox(
            left=split_x,
            top=bbox.top,
            width=bbox.width * (1 - split_ratio),
            height=bbox.height
        )
        value_geometry = Geometry(bounding_box=value_bbox)
        
        return key_geometry, value_geometry
    
    def _filter_and_deduplicate(self, pairs: List[KeyValuePair]) -> List[KeyValuePair]:
        """Filter and remove duplicate key-value pairs"""
        # Remove duplicates based on key-value text
        seen = set()
        filtered_pairs = []
        
        for pair in pairs:
            key_value_tuple = (pair.key.lower().strip(), pair.value.lower().strip())
            if key_value_tuple not in seen:
                seen.add(key_value_tuple)
                filtered_pairs.append(pair)
        
        # Sort by confidence
        filtered_pairs.sort(key=lambda x: (x.key_confidence + x.value_confidence) / 2, reverse=True)
        
        return filtered_pairs
