import os
from typing import Dict, Any

class Config:
    """Application configuration"""
    
    # API Configuration
    API_TITLE = "DocTR Text Extraction API"
    API_DESCRIPTION = "Extract key-value pairs from documents using docTR, similar to Amazon Textract"
    API_VERSION = "1.0.0"
    
    # Server Configuration
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", 8000))
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"
    
    # File Upload Configuration
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    SUPPORTED_EXTENSIONS = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.tif', '.bmp', '.webp'}
    SUPPORTED_MIME_TYPES = {
        'application/pdf',
        'image/png',
        'image/jpeg',
        'image/tiff',
        'image/bmp',
        'image/webp'
    }
    
    # DocTR Configuration
    DOCTR_DET_ARCH = os.getenv("DOCTR_DET_ARCH", "db_resnet50")
    DOCTR_RECO_ARCH = os.getenv("DOCTR_RECO_ARCH", "crnn_vgg16_bn")
    
    # Processing Configuration
    DEFAULT_CONFIDENCE_THRESHOLD = 0.5
    PDF_DPI = 200
    
    # Key-Value Extraction Configuration
    KV_HORIZONTAL_THRESHOLD = 0.3
    KV_VERTICAL_THRESHOLD = 0.05
    
    # Logging Configuration
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def get_doctr_config(cls) -> Dict[str, Any]:
        """Get docTR model configuration"""
        return {
            "det_arch": cls.DOCTR_DET_ARCH,
            "reco_arch": cls.DOCTR_RECO_ARCH,
            "pretrained": True
        }
