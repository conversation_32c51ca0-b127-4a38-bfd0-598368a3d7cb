from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, Request
from fastapi.responses import JSONResponse
import logging
import traceback
from typing import Dict, Any

logger = logging.getLogger(__name__)

class DocumentProcessingError(Exception):
    """Custom exception for document processing errors"""
    def __init__(self, message: str, error_code: str = "PROCESSING_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class FileValidationError(Exception):
    """Custom exception for file validation errors"""
    def __init__(self, message: str, error_code: str = "VALIDATION_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ModelInitializationError(Exception):
    """Custom exception for model initialization errors"""
    def __init__(self, message: str, error_code: str = "MODEL_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

async def document_processing_exception_handler(request: Request, exc: DocumentProcessingError):
    """Handle document processing exceptions"""
    logger.error(f"Document processing error: {exc.message}")
    return JSONResponse(
        status_code=422,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "type": "DocumentProcessingError"
            }
        }
    )

async def file_validation_exception_handler(request: Request, exc: FileValidationError):
    """Handle file validation exceptions"""
    logger.error(f"File validation error: {exc.message}")
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "type": "FileValidationError"
            }
        }
    )

async def model_initialization_exception_handler(request: Request, exc: ModelInitializationError):
    """Handle model initialization exceptions"""
    logger.error(f"Model initialization error: {exc.message}")
    return JSONResponse(
        status_code=503,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "type": "ModelInitializationError"
            }
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unexpected error: {str(exc)}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while processing your request",
                "type": "InternalServerError"
            }
        }
    )

def create_error_response(
    status_code: int,
    error_code: str,
    message: str,
    error_type: str = "Error"
) -> Dict[str, Any]:
    """Create standardized error response"""
    return {
        "error": {
            "code": error_code,
            "message": message,
            "type": error_type
        }
    }
